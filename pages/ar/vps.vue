<template>
	<view class="container" :style="{ paddingTop: navBarInfo.navHeight + 'px' }">
		<navbar
			:title="locationName"
			show-nav-back
			is-landscape
			:opacity="1"
			:on-nav-click="onNavBack"
		/>
		<xr-ar-plane
			id="main-frame"
			ref="arRef"
			disable-scroll
			:width="frameParams.renderWidth"
			:height="frameParams.renderHeight"
			:style="frameStyle"
			:initial-vps-tracked="initialVpsTracked"
			:transform-matrix="transformMatrix"
			:on-ar-ready="handleArReady"
			:on-ar-init="handleArInit"
			:on-ar-tracked="handleArTracked"
			:on-ar-lost="handleArLost"
			:on-camera-pose-tick="handleCameraPoseTick"
			:asset-list="assetList"
			:on-asset-list-loaded="handleAssetListLoaded"
		/>
		<!-- 重定位按钮 -->
		<image
			v-if="isVpsExperienceClicked && !showScreenshotPreview"
			:src="`${imgBaseUrl}visual/visual-refresh-btn.png`"
			class="relocate-button"
			:style="{
				top: navBarInfo.navHeight + 16 + 'px'
			}"
			@click="requestVps"
		/>
		<!-- 按钮容器 -->
		<view class="button-container">
			<view v-if="showExperienceButton" class="vps-button" @click="startExperience">
				<text>开始体验</text>
			</view>
			<image
				v-if="showScreenshotButton"
				:src="`${imgBaseUrl}visual/visual-ar-screenshot-btn.png`"
				class="screenshot-button"
				@click="takeScreenshot"
			/>
		</view>

		<!-- 截图预览组件 -->
		<screenshot-preview
			:visible="showScreenshotPreview"
			:screenshot-data="screenshotData"
			:location-name="locationName"
			@close="closeScreenshotPreview"
			@save="handleScreenshotSave"
		/>

		<!-- 保存用的隐藏canvas -->
		<canvas canvas-id="saveCanvas" class="save-canvas" :style="{ width: '420px', height: '290px' }">
		</canvas>
		<custom-notice v-model:notice="noticeConfig">
			<template #icon>
				<image class="notice-icon" :src="`${imgBaseUrl}visual/visual-notice-ar-initcontent.png`" />
			</template>
			<template #content>
				<view class="loading-container">
					<text class="loading-title">{{ noticeConfig.title }}</text>
					<view class="progress-container">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: loadingProgress + '%' }"></view>
						</view>
						<text class="loading-percent">{{ loadingProgressText }}%</text>
					</view>
				</view>

				<view class="loading-container">
					<text class="loading-title">资产加载中...</text>
					<view class="progress-container">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: assetLoadingProgress + '%' }"></view>
						</view>
						<text class="loading-percent">{{ assetLoadingProgressText }}%</text>
					</view>
				</view>
			</template>
		</custom-notice>
	</view>
</template>

<script setup>
import { reactive, computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import {
	calculateTransformationMatrix,
	radianToAngle,
	matrixLeftToRightMatrixY,
	matrixToPose
} from '@/wxcomponents/utils/transformUtils'
import { yuvToImage, cleanupYuvImageUtils, getMemoryUsage } from '@/wxcomponents/utils/yuvImageUtils'
import { AnchorData, AssetData } from './anchor-data'
import { FormData } from '@/utils/form-data/formData'
import VpsService from '@/service/vps'
import performanceMonitor from '@/utils/performanceMonitor'
import ScreenshotPreview from '@/components/screenshot-preview/screenshot-preview.vue'
import CustomNotice from '@/components/notice/notice.vue'
import { noticeConfigs, NoticeEnum } from './settings/config'
import { imgBaseUrl } from '@/config'

const store = useStore()
const innerAudioContext = uni.createInnerAudioContext()
innerAudioContext.loop = true

const xr = wx.getXrFrameSystem()
const arRef = ref(null)
const lastVpsRoamingSuccess = ref(false)
const arTracked = ref(false)
const transformMatrix = ref(null)
const initialVpsTracked = ref(false)
const userPose = ref(null)
const arRawInfo = ref(null)
const anchorList = ref([])
const currentProjectId = ref('1939')
const currentLongitude = ref('116.329943')
const currentLatitude = ref('39.992194')
const assetList = ref([])
const assetListLoaded = ref(false)
const initialAssetListLoad = ref(true)
const noticeConfig = ref({ ...noticeConfigs[NoticeEnum.ArIniting], show: true, manualShow: true })
const locationName = ref('')
const loadingProgress = ref(0)
const loadingProgressText = ref('00')
const assetLoadingProgress = ref(0)
const assetLoadingProgressText = ref('00')

// vps按钮相关状态
const isVpsExperienceClicked = ref(false)
// 截图相关状态
const showScreenshotPreview = ref(false)
const screenshotData = ref('')

const RequestStatus = {
	IDLE: 'idle',
	PENDING: 'pending',
	SUCCESS: 'success',
	FAIL: 'fail',
	ABORTED: 'aborted'
}

const requestVpsTask = ref(null)
const requestStatus = ref(RequestStatus.IDLE)

// 添加倒计时相关的状态
const countdownText = ref('开始体验')
const isCountingDown = ref(false)
const vpsRetryCount = ref(0)
const MAX_RETRY_COUNT = 2

const frameParams = reactive({
	width: 0,
	height: 0,
	renderWidth: 0,
	renderHeight: 0
})

const navBarInfo = computed(() => store.getters.navbarInfo)
// 只有当AR初始化完成且资产加载完成时才显示体验按钮
const showExperienceButton = computed(() => !isVpsExperienceClicked.value && allReady.value)
const showScreenshotButton = computed(() => initialVpsTracked.value && !showScreenshotPreview.value)

const frameStyle = computed(() => ({
	width: frameParams.width + 'px',
	height: frameParams.height + 'px',
	top: 0,
	left: 0,
	display: 'block'
}))

onLoad(async (options) => {
	currentProjectId.value = options.projectId
	locationName.value = options.locationName
	try {
		const locationObj = JSON.parse(options.location)
		currentLongitude.value = locationObj.longitude
		currentLatitude.value = locationObj.latitude
		console.log('options.audioUrl: ', options.audioUrl)
		if (options.audioUrl) {
			innerAudioContext.src = decodeURIComponent(options.audioUrl)
		}
	} catch (error) {
		console.error('location is not a valid JSON string', error)
	}
	const { windowWidth, windowHeight, pixelRatio } = uni.getWindowInfo()

	console.log(
		'windowWidth: ',
		windowWidth,
		'windowHeight: ',
		windowHeight,
		'pixelRatio: ',
		pixelRatio
	)
	frameParams.width = windowWidth
	frameParams.height = windowHeight
	frameParams.renderWidth = windowWidth * pixelRatio
	frameParams.renderHeight = windowHeight * pixelRatio

	// 预加载项目下所有资产
	await loadProjectAssets(currentProjectId.value)

	// 开始模拟加载进度
	simulateLoadingProgress()

	// 初始化性能监控
	initPerformanceMonitor()
})

onUnload(() => {
	// 取消进行中的VPS请求
	if (requestVpsTask.value && requestStatus.value === RequestStatus.PENDING) {
		requestVpsTask.value.abort()
		requestVpsTask.value = null
	}

	// 停止性能监控
	performanceMonitor.stopMonitoring()

	innerAudioContext.stop()

	// 清理AR相关引用
	cleanupArResources()

	// 强制垃圾回收
	if (typeof wx !== 'undefined' && wx.triggerGC) {
		wx.triggerGC()
	}
})

const onNavBack = () => {
	// 取消进行中的VPS请求
	if (requestVpsTask.value && requestStatus.value === RequestStatus.PENDING) {
		requestVpsTask.value.abort()
		requestVpsTask.value = null
	}

	// 停止性能监控
	performanceMonitor.stopMonitoring()

	innerAudioContext.stop()

	// 清理AR相关引用
	cleanupArResources()

	// 强制垃圾回收
	if (typeof wx !== 'undefined' && wx.triggerGC) {
		wx.triggerGC()
	}
	uni.navigateBack({
		delta: 1
	})
}

const tryGetFirstGlb = (modelUrls) => {
	const firstGlb = (modelUrls || []).find(
		(url) => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf')
	)
	return firstGlb
}

/**
 * 优化的资产加载函数，添加内存管理
 */
const loadProjectAssets = async (projectId) => {
	try {
		// 在加载新资产前，先清理旧资产
		if (assetList.value.length > 0) {
			console.log('清理旧资产列表...')
			assetList.value = []
			// 触发垃圾回收
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}
		}

		// 重置资产加载进度
		assetLoadingProgress.value = 0
		assetLoadingProgressText.value = '00'

		// 开始模拟资产加载进度
		const interval = setInterval(() => {
			const increment = Math.floor(Math.random() * 10) + 1
			assetLoadingProgress.value = Math.min(assetLoadingProgress.value + increment, 95) // 最多到95%，等待实际加载完成
			assetLoadingProgressText.value =
				assetLoadingProgress.value < 10
					? `0${assetLoadingProgress.value}`
					: `${assetLoadingProgress.value}`
		}, 100)

		const { data: assetsData } = await VpsService.getAssetList(projectId)
		assetList.value = (assetsData || [])
			.filter((asset) => tryGetFirstGlb(asset.models))
			.map((asset) => new AssetData(asset.assetId, 'gltf', tryGetFirstGlb(asset.models)))

		// 停止进度模拟，但不立即设置为100%，等待handleAssetListLoaded回调时再设置为100%
		clearInterval(interval)
	} catch (error) {
		console.error(`加载项目 ${projectId} 资产时出错:`, error)
		assetList.value = []
		initialAssetListLoad.value = false
		setCustomNotice(NoticeEnum.ProjectLoadFailed)
	}
}
const arReady = ref(false)
// AR摄像头已启用
const handleArReady = async () => {
	console.log('handleArReady')
}

const allReady = computed(() => arReady.value && !initialAssetListLoad.value)

watch(allReady, (newVal) => {
	if (newVal) {
		setCustomNotice(NoticeEnum.ArIniting, false, false)
	}
})

watch(showScreenshotButton, (newVal) => {
	if (newVal) {
		innerAudioContext.play()
	} else {
		innerAudioContext.pause()
	}
})

// 模拟AR初始化进度
const simulateLoadingProgress = () => {
	loadingProgress.value = 0
	loadingProgressText.value = '00'

	const interval = setInterval(() => {
		const increment = Math.floor(Math.random() * 10) + 1
		loadingProgress.value = Math.min(loadingProgress.value + increment, 95)
		loadingProgressText.value =
			loadingProgress.value < 10 ? `0${loadingProgress.value}` : `${loadingProgress.value}`

		// 当AR已初始化完成时停止并设置为100%
		if (arTracked.value) {
			clearInterval(interval)
			loadingProgress.value = 100
			loadingProgressText.value = '100'
		}
	}, 100) // 每100毫秒更新一次
}

// 初次预加载资产列表成功
const handleAssetListLoaded = async (isLoaded) => {
	assetListLoaded.value = isLoaded
	initialAssetListLoad.value = false

	// 资产真正加载完成时，才设置为100%
	if (isLoaded) {
		assetLoadingProgress.value = 100
		assetLoadingProgressText.value = '100'
	} else {
		setCustomNotice(NoticeEnum.ProjectLoadFailed)
	}
}

// SLAM追踪初始化完毕
const handleArInit = () => {
	arTracked.value = true
	arReady.value = true
	// 确保AR初始化进度为100%
	loadingProgress.value = 100
	loadingProgressText.value = '100'
}

// SLAM丢失
const handleArLost = () => {
	arTracked.value = false
	setCustomNotice(NoticeEnum.ArTrackingRestoring)
}
// SLAM丢失后，AR再次初始化成功
let arStableTimer = null

const handleArTracked = () => {
	console.log('[handleArTracked] AR追踪恢复')
	arTracked.value = true
	uni.hideLoading()

	// 清除之前的定时器（如果存在）
	if (arStableTimer) {
		clearTimeout(arStableTimer)
		arStableTimer = null
	}

	// 设置3秒后的检测
	arStableTimer = setTimeout(async () => {
		// 确保arTracked仍为true
		if (arTracked.value) {
			if (initialVpsTracked.value) {
				console.log('[handleArTracked] 初始VPS已追踪，稳定3秒后重新请求VPS')
				try {
					await requestVps()
					console.log('[handleArTracked] VPS请求完成')
				} catch (error) {
					console.error('[handleArTracked] VPS请求失败:', error)
				}
			} else {
				console.log('[handleArTracked] 初始VPS未追踪，跳过VPS请求')
			}
		} else {
			console.log('[handleArTracked] 3秒后arTracked已变为false，取消VPS请求')
		}
	}, 3000)
}

// 性能监控相关函数
const initPerformanceMonitor = () => {
	// 重置性能监控器状态
	performanceMonitor.reset()

	// 设置性能监控回调
	performanceMonitor.setCallbacks({
		onPerformanceAlert: (type, data) => {
			handlePerformanceAlert(type, data)
		},
		onMemoryWarning: (warningCount) => {
			console.warn(`内存警告 #${warningCount}，尝试清理资源`)
			// 主动清理一些可释放的资源
			cleanupOptionalResources()
		}
	})

	// 开始监控
	performanceMonitor.startMonitoring()
	console.log('性能监控已初始化')
}

const handlePerformanceAlert = (type) => {
	if (type === 'critical_fps' || type === 'memory_warning') {
		setCustomNotice(NoticeEnum.DeviceMemoryLow)
		return
	}

	if (type === 'low_fps') {
		setCustomNotice(NoticeEnum.FrameRateLow, false)
		return
	}
}

/**
 * 清理可选资源，在内存警告时调用
 */
const cleanupOptionalResources = () => {
	console.log('开始清理可选资源以释放内存...')

	// 获取当前YUV内存使用情况
	const memoryUsage = getMemoryUsage()
	console.log('当前YUV内存使用情况:', memoryUsage)

	// 清理截图数据
	if (screenshotData.value) {
		screenshotData.value = ''
		console.log('已清理截图数据')
	}

	// 清理历史AR原始数据
	if (arRawInfo.value) {
		// 保留最新的，清理历史缓存
		console.log('已清理AR原始数据缓存')
	}

	// 清理YUV处理内存
	try {
		cleanupYuvImageUtils()
		console.log('已清理YUV处理内存')
	} catch (error) {
		console.error('清理YUV处理内存时出错:', error)
	}

	// 强制垃圾回收
	if (typeof wx !== 'undefined' && wx.triggerGC) {
		wx.triggerGC()
		console.log('已触发垃圾回收')
	}

	// 再次检查内存使用情况
	const newMemoryUsage = getMemoryUsage()
	console.log('清理后YUV内存使用情况:', newMemoryUsage)
}

// 开始体验
const startExperience = () => {
	isVpsExperienceClicked.value = true
	requestVps()
}

// 用于记录VPS纠偏后的相机位姿
const handleCameraPoseTick = (evt) => {
	//console.log('handleCameraPoseTick: '+JSON.stringify(evt));
	const { cameraPos, cameraQuat, arRawData } = evt

	if (!arRawInfo.value) {
		arRawInfo.value = arRawData
	}
	userPose.value = {
		position: { x: cameraPos.x, y: cameraPos.y, z: cameraPos.z },
		quaternion: cameraQuat
	}
}
// 申请初始定位
const requestVps = async () => {
	if (!arTracked.value) {
		console.log('AR未追踪，跳过VPS请求')
		return
	}

	// 检查是否有进行中的请求
	if (requestStatus.value === RequestStatus.PENDING) {
		console.log('VPS请求进行中，跳过重复请求')
		return
	}

	//此处使用原始相机的位姿，是因为原始相机位姿在经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
	let queryPose = {
		position: xr.Vector3.createFromNumber(
			userPose.value.position.x,
			userPose.value.position.y,
			userPose.value.position.z
		),
		quaternion: userPose.value.quaternion
	}

	try {
		requestVpsTask.value = await saveCurrentFrame(queryPose)
	} catch (error) {
		console.error('VPS请求失败:', error)
		requestStatus.value = RequestStatus.FAIL
	}
}

const saveCurrentFrame = async (queryPose) => {
	let imageBuffer = null
	let formData = null

	try {
		console.warn('saveCurrentFrame: ', arRawInfo.value)
		if (!arRawInfo.value) {
			console.log('arRawInfo is empty')
			return
		}
		uni.showLoading({
			title: '定位中...'
		})

		const { yBuffer, uvBuffer, width, height, intrinsics } = arRawInfo.value
		// 图像处理，注意内存管理
		imageBuffer = await yuvToImage(yBuffer, uvBuffer, width, height)

		const vpsIntrinsics = [
			intrinsics[0],
			intrinsics[4],
			intrinsics[6],
			intrinsics[7],
			width,
			height
		]

		formData = new FormData()
		formData.appendFile('file', imageBuffer, 'pic.jpg')
		formData.append('latitude', currentLatitude.value)
		formData.append('longitude', currentLongitude.value)
		formData.append('projectId', currentProjectId.value)
		formData.append('userId', '6') // TODO: 替换真实用户ID
		formData.append('type', 2)
		formData.append('intrinsics', vpsIntrinsics)

		const requestData = formData.getData()
		console.warn('requestData: ', JSON.stringify(requestData), requestData)
		requestStatus.value = RequestStatus.PENDING
		const { data: vpsInfo } = await VpsService.vpsRoam({
			params: requestData.buffer,
			contentType: requestData.contentType
		})

		console.warn('vpsInfo: ', vpsInfo)
		requestStatus.value = RequestStatus.SUCCESS

		const vpsPosition = { x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2] }
		const vpsQuaternion = xr.Quaternion.createFromNumber(
			vpsInfo.qcw[1],
			vpsInfo.qcw[2],
			vpsInfo.qcw[3],
			vpsInfo.qcw[0]
		)
		const vpsPose = {
			position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
			quaternion: vpsQuaternion
		}
		const cameraEuler = queryPose.quaternion.toEulerAngles()
		console.log(
			'--------queryPose的值是------: position: x: ' +
				queryPose.position.x +
				', y: ' +
				queryPose.position.y +
				', z: ' +
				queryPose.position.z +
				', rotation: x: ' +
				radianToAngle(cameraEuler.x) +
				', y: ' +
				radianToAngle(cameraEuler.y) +
				', z: ' +
				radianToAngle(cameraEuler.z)
		)

		console.log(
			' --------vpsPose的值是------:' +
				JSON.stringify(vpsPose.position) +
				',' +
				'vpsPose rotation:' +
				JSON.stringify(vpsPose.quaternion.toEulerAngles())
		)

		transformMatrix.value = calculateTransformationMatrix(queryPose, vpsPose)
		vpsRetryCount.value = 0
		lastVpsRoamingSuccess.value = true

		uni.hideLoading()
		uni.showToast({
			title: '定位成功',
			icon: 'success'
		})
		const vpsAnchorList = vpsInfo.deltaPositionList
		const anchors = []

		// 添加VPS数据验证日志
		console.log('[VPS] 接收到的完整VPS响应:', JSON.stringify(vpsInfo))
		console.log('[VPS] deltaPositionList长度:', vpsAnchorList?.length)
		console.log('[VPS] deltaPositionList类型:', typeof vpsAnchorList)

		if (!vpsAnchorList || !Array.isArray(vpsAnchorList)) {
			console.error('[VPS] deltaPositionList无效:', vpsAnchorList)
			return
		}

		vpsAnchorList.forEach((anchor, index) => {
			console.log(`[VPS] 处理锚点 ${index}:`, JSON.stringify(anchor))

			// 验证必要字段
			if (!anchor) {
				console.error(`[VPS] 锚点 ${index} 为null或undefined`)
				return
			}

			if (!anchor.position || !anchor.rotation || !anchor.scale) {
				console.error(`[VPS] 锚点 ${index} 缺少必要字段:`, {
					hasPosition: !!anchor.position,
					hasRotation: !!anchor.rotation,
					hasScale: !!anchor.scale,
					hasAnchorId: !!anchor.anchorId,
					hasAssetId: !!anchor.assetId,
					hasModels: !!anchor.models
				})
				return
			}

			let anchorPositionArray, anchorRotationArray, anchorScaleArray

			try {
				anchorPositionArray = JSON.parse(anchor.position)
				anchorRotationArray = JSON.parse(anchor.rotation)
				anchorScaleArray = JSON.parse(anchor.scale)

				console.log(`[VPS] 锚点 ${index} 解析后数据:`, {
					position: anchorPositionArray,
					rotation: anchorRotationArray,
					scale: anchorScaleArray
				})
			} catch (parseError) {
				console.error(`[VPS] 锚点 ${index} JSON解析失败:`, parseError)
				console.error(`[VPS] 原始数据:`, {
					position: anchor.position,
					rotation: anchor.rotation,
					scale: anchor.scale
				})
				return
			}

			// 验证数组长度和数值有效性
			if (!Array.isArray(anchorPositionArray) || anchorPositionArray.length < 3) {
				console.error(`[VPS] 锚点 ${index} position数组无效:`, anchorPositionArray)
				return
			}

			if (!Array.isArray(anchorRotationArray) || anchorRotationArray.length < 4) {
				console.error(`[VPS] 锚点 ${index} rotation数组无效:`, anchorRotationArray)
				return
			}

			if (!Array.isArray(anchorScaleArray) || anchorScaleArray.length < 3) {
				console.error(`[VPS] 锚点 ${index} scale数组无效:`, anchorScaleArray)
				return
			}

			// 检查是否包含NaN或null
			const allValues = [...anchorPositionArray.slice(0, 3), ...anchorRotationArray.slice(0, 4), ...anchorScaleArray.slice(0, 3)]
			const invalidValues = allValues.filter(v => v === null || v === undefined || isNaN(Number(v)))

			if (invalidValues.length > 0) {
				console.error(`[VPS] 锚点 ${index} 包含无效数值:`, {
					invalidValues,
					position: anchorPositionArray.slice(0, 3),
					rotation: anchorRotationArray.slice(0, 4),
					scale: anchorScaleArray.slice(0, 3)
				})
				return
			}

			console.log(`[VPS] 锚点 ${index} 数据验证通过，开始创建XR对象`)
			//从云测返回的anchor位姿
			let vpsAnchorPose
			try {
				console.log(`[VPS] 锚点 ${index} 创建Vector3和Quaternion`)
				vpsAnchorPose = {
					position: xr.Vector3.createFromNumber(
						anchorPositionArray[0],
						anchorPositionArray[1],
						anchorPositionArray[2]
					),
					quaternion: xr.Quaternion.createFromNumber(
						anchorRotationArray[1],
						anchorRotationArray[2],
						anchorRotationArray[3],
						anchorRotationArray[0]
					)
				}
				console.log(`[VPS] 锚点 ${index} XR对象创建成功`)
				console.log('--------Anchor的VPSPose是------：' + JSON.stringify(vpsAnchorPose))
			} catch (xrError) {
				console.error(`[VPS] 锚点 ${index} XR对象创建失败:`, xrError)
				console.error(`[VPS] 使用的数值:`, {
					position: [anchorPositionArray[0], anchorPositionArray[1], anchorPositionArray[2]],
					rotation: [anchorRotationArray[1], anchorRotationArray[2], anchorRotationArray[3], anchorRotationArray[0]]
				})
				return
			}

			const scale = { x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2] }
			console.log(`[VPS] 锚点 ${index} scale对象:`, scale)

			let vpsAnchorMatrix, Tmatrix, slam_anchor_matrix, convertedMatrix, convertedPose

			try {
				console.log(`[VPS] 锚点 ${index} 开始矩阵计算`)

				vpsAnchorMatrix = xr.Matrix4.composeTQS(
					vpsAnchorPose.position,
					vpsAnchorPose.quaternion,
					xr.Vector3.ONE
				)
				console.log(`[VPS] 锚点 ${index} vpsAnchorMatrix创建成功`)

				if (!transformMatrix.value) {
					console.error(`[VPS] 锚点 ${index} transformMatrix为空:`, transformMatrix.value)
					return
				}

				Tmatrix = new xr.Matrix4()
				Tmatrix.setArray(transformMatrix.value)
				console.log('--------Anchor的转换矩阵是------：' + JSON.stringify(matrixToPose(Tmatrix)))

				slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)
				console.log(`[VPS] 锚点 ${index} slam_anchor_matrix计算成功`)

				convertedMatrix = matrixLeftToRightMatrixY(slam_anchor_matrix)
				console.log(`[VPS] 锚点 ${index} convertedMatrix计算成功`)

				convertedPose = matrixToPose(convertedMatrix)
				console.log(`[VPS] 锚点 ${index} convertedPose计算成功`)
			} catch (matrixError) {
				console.error(`[VPS] 锚点 ${index} 矩阵计算失败:`, matrixError)
				console.error(`[VPS] 矩阵计算错误堆栈:`, matrixError.stack)
				return
			}

			let convertedEuler, transformedPose2, anchorUrl, anchorData

			try {
				convertedEuler = convertedPose.quaternion.toEulerAngles()
				transformedPose2 = {
					position: {
						x: convertedPose.position.x,
						y: convertedPose.position.y,
						z: convertedPose.position.z
					},
					rotation: { x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z }
				}
				console.log(`[VPS] 锚点 ${index} 最终位姿计算完成:`, transformedPose2)

				// 验证模型URL
				console.log(`[VPS] 锚点 ${index} 检查模型URL, models:`, anchor.models)
				anchorUrl = tryGetFirstGlb(anchor.models)
				console.log(`[VPS] 锚点 ${index} 获取到的GLB URL:`, anchorUrl)

				if (!anchorUrl) {
					console.warn(`[VPS] 锚点 ${index} 没有有效的GLB模型URL`)
					return
				}

				// 验证必要的ID字段
				if (!anchor.anchorId || !anchor.assetId) {
					console.error(`[VPS] 锚点 ${index} 缺少ID字段:`, {
						anchorId: anchor.anchorId,
						assetId: anchor.assetId
					})
					return
				}

				console.log(`[VPS] 锚点 ${index} 创建AnchorData对象`)
				anchorData = new AnchorData(
					anchor.anchorId,
					'anchor-' + anchor.anchorId,
					transformedPose2.position,
					transformedPose2.rotation,
					scale,
					anchor.assetId,
					anchorUrl
				)

				anchors.push(anchorData)
				console.log(`[VPS] 锚点 ${index} 成功添加到anchors数组`)
			} catch (finalError) {
				console.error(`[VPS] 锚点 ${index} 最终处理失败:`, finalError)
				console.error(`[VPS] 最终处理错误堆栈:`, finalError.stack)
			}
		})

		console.log(`[VPS] 锚点处理完成，成功处理 ${anchors.length} 个锚点`)
		console.log('[VPS] 最终anchors数组:', anchors.map(a => ({
			id: a.id,
			name: a.name,
			assetId: a.assetId,
			position: a.position,
			rotation: a.rotation,
			scale: a.scale
		})))

		initialVpsTracked.value = true
		anchorList.value = anchors
		// TODO: 加入临时模型加载缓冲
		await refreshAnchorList()
	} catch (err) {
		console.log(JSON.stringify(err) + ', retry saveCurrentFrame')
		uni.hideLoading()
		requestStatus.value = RequestStatus.FAIL

		// 检查重试次数
		if (vpsRetryCount.value < MAX_RETRY_COUNT) {
			vpsRetryCount.value++

			// setCustomNotice(NoticeEnum.LocationFailed)

			lastVpsRoamingSuccess.value = false

			// 直接重试，不设置延迟
			await saveCurrentFrame(queryPose)
		} else {
			// 达到最大重试次数
			setCustomNotice(NoticeEnum.LocationFailed)

			// 重置计数器，为下一次手动尝试做准备
			vpsRetryCount.value = 0
			lastVpsRoamingSuccess.value = false
		}
	} finally {
		// 清理临时资源，防止内存泄漏
		if (imageBuffer) {
			imageBuffer = null
		}
		if (formData) {
			formData = null
		}
		// 触发垃圾回收
		if (typeof wx !== 'undefined' && wx.triggerGC) {
			wx.triggerGC()
		}
	}
}

const refreshAnchorList = async () => {
	console.log('[refreshAnchorList] 开始刷新锚点列表')

	if (!arRef.value) {
		console.log('[refreshAnchorList] arRef is undefined')
		return
	}

	if (anchorList.value && anchorList.value.length > 0) {
		console.log(`[refreshAnchorList] 准备处理 ${anchorList.value.length} 个锚点`)

		if (!assetListLoaded.value) {
			console.log('[refreshAnchorList] 显示资产加载中')
			uni.showLoading({ title: '资产加载中' })
		}

		try {
			// 改为串行执行，避免并发问题
			console.log('[refreshAnchorList] 开始串行处理锚点')
			for (let i = 0; i < anchorList.value.length; i++) {
				const anchor = anchorList.value[i]
				console.log(
					`[refreshAnchorList] 处理锚点 ${i + 1}/${anchorList.value.length}: ${anchor.id}` +
					' position: ' +
					JSON.stringify(anchor.position) +
					', rotation: x: ' +
					anchor.rotation.x +
					', y: ' +
					anchor.rotation.y +
					', z: ' +
					anchor.rotation.z
				)

				try {
					await arRef.value.spawnAnchorItem(anchor)
					console.log(`[refreshAnchorList] 锚点 ${anchor.id} 处理成功`)
				} catch (error) {
					console.error(`[refreshAnchorList] 锚点 ${anchor.id} 处理失败:`, error)
					// 即使单个锚点失败，也继续处理下一个
				}
			}

			console.log('[refreshAnchorList] 所有锚点处理完成')
			assetListLoaded.value = true
		} catch (error) {
			console.error('[refreshAnchorList] 锚点列表处理出错:', error)
			setCustomNotice(NoticeEnum.ProjectLoadFailed)
			assetListLoaded.value = false
		} finally {
			console.log('[refreshAnchorList] 隐藏加载状态')
			uni.hideLoading()
		}
	} else {
		console.log('[refreshAnchorList] anchorList is empty')
	}
}

const setCustomNotice = (noticeType, show = true, manualShow = false) => {
	noticeConfig.value = { ...noticeConfigs[noticeType], show, manualShow }
}

const takeScreenshot = async () => {
	if (!arRef.value) {
		console.log('arRef is undefined')
		return
	}
	try {
		// 获取截图
		const base64 = await arRef.value.takeScreenshot()
		screenshotData.value = base64
		// 显示截图预览
		showScreenshotPreview.value = true
	} catch (error) {
		console.error('截图失败:', error)
		uni.showToast({
			title: '截图失败',
			icon: 'error'
		})
	}
}

// 关闭截图预览
const closeScreenshotPreview = () => {
	showScreenshotPreview.value = false
}

// 处理截图保存
const handleScreenshotSave = (savedPath) => {
	console.log('截图已保存:', savedPath)
	closeScreenshotPreview()
}

/**
 * 清理AR相关资源，防止内存泄漏
 */
const cleanupArResources = () => {
	console.log('开始清理AR相关资源...')

	// 清理引用变量
	transformMatrix.value = null
	userPose.value = null
	arRawInfo.value = null
	anchorList.value = []
	assetList.value = []
	screenshotData.value = ''

	// 重置状态
	initialVpsTracked.value = false
	lastVpsRoamingSuccess.value = false
	arTracked.value = false
	assetListLoaded.value = false
	isVpsExperienceClicked.value = false
	showScreenshotPreview.value = false

	// 重置VPS相关状态
	requestStatus.value = RequestStatus.IDLE
	vpsRetryCount.value = 0
	countdownText.value = '开始体验'
	isCountingDown.value = false

	// 清理YUV图像处理相关内存
	try {
		cleanupYuvImageUtils()
		console.log('YUV图像处理资源已清理')
	} catch (error) {
		console.error('清理YUV图像处理资源时出错:', error)
	}

	console.log('AR相关资源清理完成')
}
</script>

<style scoped>
.screenshot-image {
	position: absolute;
	top: 32rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 840rpx;
	height: 466rpx;
	z-index: 1000;
}

.container {
	width: 100vw;
	height: 100vh;
	position: relative;
	box-sizing: border-box;
}

.relocate-button {
	position: absolute;
	right: 21px;
	width: 48px;
	height: 48px;
	z-index: 1000;
}

.button-container {
	display: flex;
	justify-content: center;
	align-items: flex-end;
	padding-bottom: 24px;
	box-sizing: border-box;
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 72px;
	z-index: 1000;
}

.screenshot-button {
	width: 64px;
	height: 64px;
}

.vps-button {
	width: 109px;
	height: 36px;
	border-radius: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #ffffff;
	font-size: 17px;
	font-weight: 500;
	letter-spacing: 1px;
	box-shadow: 1px 1px 4.5px 0px #40720426;
	font-family: Douyin Sans;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-ar-save-btn.png');
	background-size: cover;
	background-repeat: no-repeat;
}

.save-canvas {
	position: absolute;
	top: -9999px;
	left: -9999px;
	opacity: 0;
}

.notice-icon {
	width: 110px;
	height: 59px;
}

.loading-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 39px;
}

.loading-title {
	font-size: 16px;
	font-weight: 500;
	letter-spacing: 0.02em;
}

.progress-container {
	display: flex;
	align-items: center;
	justify-items: center;
	gap: 8px;
	width: 100%;
	height: 17px;
	/* background-color: rgba(255, 255, 255, 0.2); */
	/* overflow: hidden; */
}

.progress-bar {
	position: relative;
	width: 203px;
	height: 4px;
	border-radius: 2px;
	background-color: rgba(255, 255, 255, 0.2);
	overflow: hidden;
}

.progress-fill {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 0%;
	background-color: #ff9d30;
	border-radius: 2px;
	transition: width 0.3s ease;
}

.loading-percent {
	font-size: 12px;
	color: #ffffff;
	letter-spacing: 0.02em;
}
</style>
