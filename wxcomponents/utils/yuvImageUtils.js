import { loadWasmFromAssets } from './wasmLoader';
let wasmExports = null;
let memoryBuffer = null;
let memBuffer = null;
let startTimeStamp

// 内存管理配置
const MEMORY_CONFIG = {
  MAX_BUFFER_SIZE: 50 * 1024 * 1024, // 50MB 最大缓冲区大小
  CLEANUP_THRESHOLD: 10 * 1024 * 1024, // 10MB 清理阈值
  MAX_UNUSED_TIME: 30000, // 30秒未使用后清理
}

let lastUsedTime = 0
let memoryUsage = 0

const initWasmExports = async () => {
  try {
    wasmExports = await loadWasmFromAssets();
  } catch (err) {
    console.error('Failed to use Wasm:', err);
  }
}

/**
 * 检查并清理内存
 */
const checkAndCleanupMemory = () => {
  const currentTime = Date.now()

  // 如果内存使用超过阈值或长时间未使用，则清理
  if (memoryUsage > MEMORY_CONFIG.CLEANUP_THRESHOLD ||
      (currentTime - lastUsedTime > MEMORY_CONFIG.MAX_UNUSED_TIME)) {
    cleanupMemory()
  }
}

/**
 * 强制清理内存
 */
const cleanupMemory = () => {
  console.log('开始清理YUV处理内存...')

  // 清理内存缓冲区
  if (memBuffer) {
    memBuffer = null
    console.log('已清理memBuffer')
  }

  if (memoryBuffer) {
    memoryBuffer = null
    console.log('已清理memoryBuffer')
  }

  // 重置内存使用统计
  memoryUsage = 0

  // 触发垃圾回收
  if (typeof wx !== 'undefined' && wx.triggerGC) {
    wx.triggerGC()
    console.log('已触发垃圾回收')
  }

  console.log('YUV处理内存清理完成')
}

/**
 * 更新内存使用统计
 */
const updateMemoryUsage = (size) => {
  memoryUsage += size
  lastUsedTime = Date.now()
}

const initMemory = (memory, totalSize) => {
  // 检查内存使用情况
  checkAndCleanupMemory()

  // 检查是否超过最大缓冲区大小
  if (totalSize > MEMORY_CONFIG.MAX_BUFFER_SIZE) {
    throw new Error(`请求的内存大小 ${totalSize} 超过最大限制 ${MEMORY_CONFIG.MAX_BUFFER_SIZE}`)
  }

  // 初始化或复用线性内存缓冲区
  if (!memBuffer || memBuffer.length < totalSize) {
    // 清理旧的缓冲区
    if (memBuffer) {
      const oldSize = memBuffer.length
      memBuffer = null
      memoryUsage -= oldSize
    }

    // 创建新的缓冲区
    memBuffer = new Uint8Array(memory.buffer)
    updateMemoryUsage(memBuffer.length)
    console.log(`创建新的内存缓冲区，大小: ${memBuffer.length} bytes`)
  }

  updateMemoryUsage(0) // 更新最后使用时间
};

const convertYuvToRgba = async (yBuffer, uvBuffer, width, height) => {
  let yUint8Array = null
  let uvUint8Array = null
  let rgbaView = null

  try {
    startTimeStamp = Date.now()
    console.log('start convertYuvToRgba')

    if (!wasmExports) {
      await initWasmExports()
    }

    const memory = wasmExports.memory;
    const totalSize = yBuffer.byteLength + uvBuffer.byteLength + 4 * width * height;

    // 初始化内存
    initMemory(memory, totalSize);

    // 定义偏移位置
    const yOffset = 0;
    const uvOffset = yOffset + yBuffer.byteLength;
    const rgbaOffset = uvOffset + uvBuffer.byteLength;

    // 创建临时Uint8Array视图
    yUint8Array = new Uint8Array(yBuffer)
    uvUint8Array = new Uint8Array(uvBuffer)

    // 写入 YUV 数据到线性内存
    memBuffer.set(yUint8Array, yOffset);
    memBuffer.set(uvUint8Array, uvOffset);

    // 调用 Wasm 函数
    wasmExports.yuv_to_rgb(yOffset, uvOffset, rgbaOffset, width, height);

    // 获取转换后的数据并复制到新的数组中（避免引用WASM内存）
    const rgbaSize = width * height * 4
    const rgbaData = new Uint8Array(rgbaSize)
    const rgbaSource = new Uint8Array(memBuffer.buffer, rgbaOffset, rgbaSize)
    rgbaData.set(rgbaSource)

    console.log('end convertYuvToRgba: ' + (Date.now() - startTimeStamp) / 1000 + 's')
    return rgbaData;
  } catch (err) {
    console.error('Failed to use Wasm:', err);
    throw err
  } finally {
    // 清理临时变量
    yUint8Array = null
    uvUint8Array = null
    rgbaView = null

    // 检查是否需要清理内存
    checkAndCleanupMemory()
  }
};

const drawToCanvas = (rgba, width, height) => {
  const startTimeStamp = Date.now();
  console.log('start drawToCanvas');

  let canvas = null
  let ctx = null
  let imageData = null

  try {
    // 创建离屏画布
    canvas = wx.createOffscreenCanvas({
      type: '2d'
    });
    ctx = canvas.getContext('2d');

    // 设置画布尺寸
    canvas.width = width;
    canvas.height = height;

    // 创建ImageData并设置数据
    imageData = ctx.createImageData(width, height);
    imageData.data.set(rgba);

    ctx.putImageData(imageData, 0, 0);

    console.log('end drawToCanvas: ' + (Date.now() - startTimeStamp) / 1000 + 's');

    return canvas;
  } catch (error) {
    console.error('drawToCanvas error:', error)
    throw error
  } finally {
    // 清理临时变量引用
    imageData = null
    ctx = null
    // 注意：不要清理canvas，因为需要返回给调用者
  }
};

const saveBase64ToFile = (base64) => {
    startTimeStamp = Date.now()
    const fileManager = wx.getFileSystemManager();
    const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

    fileManager.writeFile({
      filePath,
      data: base64, // 去掉前缀
      encoding: 'base64',
      success: () => {
        console.log('File saved successfully:', filePath);
        console.log('end saveFile: ' + (Date.now() - startTimeStamp) / 1000 + 's')
        saveToAlbum(filePath)
      },
      fail: (err) => {
        console.error('Failed to save file:', err);
      },
    });
}

const saveBufferToFile = (buffer) => {
  startTimeStamp = Date.now()
  const fileManager = wx.getFileSystemManager();
  const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

  fileManager.writeFile({
      filePath,
      data: buffer, // 去掉前缀
      encoding: 'binary',
      success: () => {
          console.log('File saved successfully:', filePath);
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
          saveToAlbum(filePath)
      },
      fail: (err) => {
          console.error('Failed to save file:', err);
      },
  });
}

const saveToAlbum = (filePath) => {
  startTimeStamp = Date.now()
  wx.saveImageToPhotosAlbum({
      filePath,
      success: () => {
          wx.showToast({ title: "Saved to album!", icon: "success" });
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
      },
      fail: (err) => {
          console.error("Failed to save to album:", err);
          wx.showToast({ title: "Save failed!", icon: "error" });
      },
  });
}

const yuvToImage = async (yBuffer, uvBuffer, width, height) => {
  let rgba = null
  let canvas = null
  let imageBase64 = null

  try {
    console.log(`开始处理YUV图像: ${width}x${height}`)

    // YUV 转 RGB
    rgba = await convertYuvToRgba(yBuffer, uvBuffer, width, height)

    // 绘制到 Canvas
    canvas = drawToCanvas(rgba, width, height)

    // 转换为base64并提取数据部分
    imageBase64 = canvas.toDataURL('image/jpeg', 0.9).split(',')[1]

    // 转换为ArrayBuffer
    const imageBuffer = wx.base64ToArrayBuffer(imageBase64)

    console.log(`YUV图像处理完成，输出大小: ${imageBuffer.byteLength} bytes`)
    return imageBuffer
  } catch (err) {
    console.error("Error processing YUV data", err);
    throw err
  } finally {
    // 清理临时变量，释放内存
    if (rgba) {
      rgba = null
    }
    if (canvas) {
      // 清理canvas上下文
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
      }
      canvas = null
    }
    if (imageBase64) {
      imageBase64 = null
    }

    // 触发垃圾回收
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      wx.triggerGC()
    }
  }
}

/**
 * 按比例裁剪 base64 格式的图片
 * @param {string} base64Image - base64 格式的图片数据（可以包含或不包含 data:image 前缀）
 * @param {number} aspectRatio - 目标宽高比（宽/高）
 * @returns {Promise<string>} - 返回裁剪后的 base64 图片
 */
const cropBase64ImageByRatio = (base64Image, aspectRatio) => {
  return new Promise((resolve, reject) => {
    startTimeStamp = Date.now()
    console.log('start cropBase64ImageByRatio')

    let canvas = null
    let ctx = null
    let img = null

    try {
      // 确保 base64 字符串格式正确（移除可能的前缀）
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '')

      // 创建离屏 canvas
      canvas = wx.createOffscreenCanvas({
        type: '2d'
      })
      ctx = canvas.getContext('2d')

      // 创建图片对象
      img = canvas.createImage()

      img.onload = () => {
        try {
          const { width, height } = img
          console.log(`Original image size: ${width}x${height}`)

          // 计算裁剪区域
          let cropX = 0
          let cropY = 0
          let cropWidth = width
          let cropHeight = height

          const originalRatio = width / height

          if (originalRatio > aspectRatio) {
            // 原图过宽，需要裁剪宽度
            cropWidth = height * aspectRatio
            cropX = (width - cropWidth) / 2
          } else if (originalRatio < aspectRatio) {
            // 原图过高，需要裁剪高度
            cropHeight = width / aspectRatio
            cropY = (height - cropHeight) / 2
          }

          // 设置 canvas 尺寸为裁剪后的尺寸
          canvas.width = cropWidth
          canvas.height = cropHeight

          // 绘制裁剪后的图片
          ctx.drawImage(
            img,
            cropX, cropY, cropWidth, cropHeight,
            0, 0, cropWidth, cropHeight
          )

          // 转换为 base64
          const croppedBase64 = canvas.toDataURL('image/jpeg', 0.9)

          console.log(`Cropped image size: ${cropWidth}x${cropHeight}`)
          console.log('end cropBase64ImageByRatio: ' + (Date.now() - startTimeStamp) / 1000 + 's')

          // 清理资源
          cleanupCropResources()

          resolve(croppedBase64)
        } catch (error) {
          cleanupCropResources()
          reject(error)
        }
      }

      img.onerror = (err) => {
        console.error('Failed to load image:', err)
        cleanupCropResources()
        reject(new Error('Failed to load image'))
      }

      // 清理资源的内部函数
      const cleanupCropResources = () => {
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height)
        }
        canvas = null
        ctx = null
        img = null
      }

      // 设置图片源为 base64 数据
      img.src = `data:image/jpeg;base64,${base64Data}`
    } catch (error) {
      reject(error)
    }
  })
}

const arrayBufferToBase64 = (buffer) => {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return wx.arrayBufferToBase64(buffer)
}


/**
 * 按比例裁剪图片，支持 ArrayBuffer 或 base64 输入
 * @param {ArrayBuffer|string} image - 图片数据，可以是 ArrayBuffer 或 base64 字符串
 * @param {number} aspectRatio - 目标宽高比（宽/高）
 * @param {string} [outputFormat='base64'] - 输出格式，'base64' 或 'buffer'
 * @returns {Promise<string|ArrayBuffer>} - 返回裁剪后的图片
 */
const cropImageByRatio = (image, aspectRatio, outputFormat = 'base64') => {
  // 判断输入类型
  const isBase64 = typeof image === 'string'

  if (isBase64) {
    return cropBase64ImageByRatio(image, aspectRatio)
      .then(base64Result => {
        if (outputFormat === 'buffer') {
          // 如果需要 buffer 输出，转换 base64 为 ArrayBuffer
          return wx.base64ToArrayBuffer(base64Result.replace(/^data:image\/\w+;base64,/, ''))
        }
        return base64Result
      })
  } else {
    // 输入是 ArrayBuffer，先转为 base64
    const base64Image = `data:image/jpeg;base64,${arrayBufferToBase64(image)}`
    return cropBase64ImageByRatio(base64Image, aspectRatio)
      .then(base64Result => {
        if (outputFormat === 'buffer') {
          // 如果需要 buffer 输出，转换 base64 为 ArrayBuffer
          return wx.base64ToArrayBuffer(base64Result.replace(/^data:image\/\w+;base64,/, ''))
        }
        return base64Result
      })
  }
}



/**
 * 全局清理函数，用于释放所有YUV处理相关的内存
 */
const cleanupYuvImageUtils = () => {
  console.log('开始全局清理YUV图像处理资源...')

  // 强制清理内存
  cleanupMemory()

  // 清理WASM相关资源
  if (wasmExports) {
    wasmExports = null
  }

  console.log('YUV图像处理资源全局清理完成')
}

/**
 * 获取当前内存使用情况
 */
const getMemoryUsage = () => {
  return {
    memoryUsage,
    lastUsedTime,
    hasMemBuffer: !!memBuffer,
    hasWasmExports: !!wasmExports,
    memBufferSize: memBuffer ? memBuffer.length : 0
  }
}

export {
  yuvToImage,
  saveBase64ToFile,
  saveBufferToFile,
  saveToAlbum,
  cropImageByRatio,
  cleanupYuvImageUtils,
  getMemoryUsage,
  cleanupMemory
}
