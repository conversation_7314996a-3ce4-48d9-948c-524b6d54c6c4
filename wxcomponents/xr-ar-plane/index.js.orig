const xr = wx.getXrFrameSystem()
import {
	composeRotationMappingMatrix,
	getTransformedPose,
	radianToAngle
} from '../utils/transformUtils'
import CameraStateManager from './camera-state'

// 临时解决方案：本地定义函数以防导入失败
const localComposeRotationMappingMatrix = function () {
	let rotationMappingMatrix = xr.Matrix4.composeTQS(
		xr.Vector3.ZERO,
		xr.Quaternion.fromEulerAngles(xr.Vector3.createFromNumber(0, (-3 * Math.PI) / 2, Math.PI)),
		xr.Vector3.ONE
	)
	rotationMappingMatrix = rotationMappingMatrix.inverse()
	const rotationMappingMatixArray = rotationMappingMatrix.toArray()
	console.log('rotationMappingMatixArray: ' + rotationMappingMatixArray)
	return rotationMappingMatixArray
}

const localRadianToAngle = function (radian) {
	return (radian * 180) / Math.PI
}

const localMatrixToPose = function (matrix) {
	const posColumnArray = matrix.getColumn(3).toArray()
	let pos = xr.Vector3.createFromNumber(posColumnArray[0], posColumnArray[1], posColumnArray[2])
	let quat = xr.Quaternion.createFromMatrix4(matrix)
	return { position: pos, quaternion: quat }
}

const localGetTransformedPose = function (transformMatrixArray, pose) {
	let slamQuaternion = pose.quaternion
	let slamMatrix = xr.Matrix4.composeTQS(pose.position, slamQuaternion, xr.Vector3.ONE)
	const transformMatrix = xr.Matrix4.createFromArray(transformMatrixArray)
	let vpsMatrix = transformMatrix.multiply(slamMatrix)
	let correctedPose = localMatrixToPose(vpsMatrix)
	return correctedPose
}

let prevPos = null

Component({
	properties: {
		initialVpsTracked: Boolean,
		transformMatrix: null,
		onArReady: Function,
		onArInit: Function,
		onArLost: Function,
		onArTracked: Function,
		onAssetListLoaded: Function,
		onCameraPoseTick: Function,
		assetList: Array
	},
	data: {
		loaded: true,
		arReady: false,
		arSystemConfig: 'modes:Plane; planeMode: 1;', // 默认基础配置
		isARSupported: false,
		isDepthMaskSupported: false,
		activeAnchorList: [],
		isArCoordSystemInit: false,
		isTracking: false, // 是否正在实时进行用户位置追踪
		cameraTrackingState: -1,
		assetListLoaded: false,
    assetListLoadCount: 0
	},
	cameraTrs: null,
	arRawData: null,
	camera: null,
	cameraStateManager: null,
	renderController: null,
	assetMap: null,
	lifetimes: {
		attached() {
			console.log('data', this.data)
			// 检测设备能力并配置AR系统
			// this.detectDeviceCapabilities();
		},
		detached() {
			this.cleanup()
		}
	},
	observers: {
		assetList: async function (assetList) {
			if (!assetList || assetList.length === 0) {
				console.log('assetList is empty')
				return
			}
			if (this.data.assetListLoaded) {
				console.log('assetList has been loaded')
				return
			}
			try {
				await this.loadAssetList(assetList)
				console.log('资产列表加载完成')
			} catch (err) {
				console.error('资产列表加载失败:', err)
        if (this.data.assetListLoadCount > 1) {
          this.data.onAssetListLoaded(false)
        }
			}
		}
	},
	methods: {
		/**
		 * 检测设备能力
		 */
		detectDeviceCapabilities() {
			try {
				// 使用设备能力检测工具
				const report = DeviceCapability.getReport()
				const { capabilities, arSystemConfig } = report

				this.setData({
					isARSupported: capabilities.isARSupported,
					isDepthMaskSupported: capabilities.isDepthMaskSupported,
					arSystemConfig: arSystemConfig
				})

				console.log('设备能力检测报告:', report)
			} catch (error) {
				console.error('设备能力检测失败:', error)
				// 使用默认配置
				this.setData({
					arSystemConfig: 'modes:Plane; planeMode: 1;'
				})
			}
		},
		handleReady: async function ({ detail }) {
			this.scene = detail.value
			this.mat = new (wx.getXrFrameSystem().Matrix4)()
			try {
				await this.loadAssetList(this.data.assetList)
			} catch (err) {
				console.error('资产列表加载失败:', err)
        if (this.data.assetListLoadCount > 1) {
          this.data.onAssetListLoaded(false)
        }
			}
		},
		handleARReady: function ({ detail }) {
			console.log('arReady', this.scene.ar.arVersion)
			this.setData({ arReady: true })
			this.cameraTrs = this.scene.getElementById('camera').getComponent(xr.Transform)
			this.camera = this.scene.getElementById('camera').getComponent(xr.Camera)
			//this.planeTracker = this.scene.getElementById('plane').getComponent(xr.ARTracker)
			this.arRawData = this.scene.ar.getARRawData()
			this.cameraStateManager = new CameraStateManager()
			// this.renderController = new RenderController(this.cameraTrs, 20.0);
			this.resumeTracking()
			this.data.onArReady()
		},
		handleOnTick(deltaTime) {
			// ===== START_TICK_SAFETY_CHECK: 添加安全检查防止退出后继续执行 =====
			// 安全检查：如果不在追踪状态，直接退出
			if (!this.data.isTracking) {
				console.log('[handleOnTick] 检测到非追踪状态，跳过tick执行')
				return
			}

			// 检查必要的对象是否存在
			if (!this.cameraTrs || !this.cameraStateManager) {
				console.log('[handleOnTick] 检测到关键对象已被清理，跳过tick执行')
				return
			}
			// ===== END_TICK_SAFETY_CHECK =====

			try {
				let worldPos = this.cameraTrs.position
				let worldQuat = this.cameraTrs.quaternion
				let originalPose = {
					position: xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z),
					quaternion: xr.Quaternion.fromEulerAngles(
						xr.Vector3.createFromNumber(
							this.cameraTrs.rotation.x,
							this.cameraTrs.rotation.y,
							this.cameraTrs.rotation.z
						)
					)
				}
				if (!prevPos) {
					prevPos = xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z)
				}
				const currCameraTrackingState = this.cameraStateManager.getCameraState(prevPos, worldPos)

				if (!this.data.isArCoordSystemInit) {
					if (currCameraTrackingState === 1) {
						console.log('isArActive: ar init')
						this.setData({
							isArCoordSystemInit: true,
							cameraTrackingState: 1
						})
						const worldEuler = worldQuat.toEulerAngles()
						console.warn('func: ', composeRotationMappingMatrix)
						console.log(
							'--------worldPose的值是------: position: x: ' +
								originalPose.position.x +
								', y: ' +
								originalPose.position.y +
								', z: ' +
								originalPose.position.z +
								', rotation: x: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.x) +
								', y: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.y) +
								', z: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.z)
						)
						// 使用本地函数作为备用
						this.arSystemTransformMatrix =
							(composeRotationMappingMatrix || localComposeRotationMappingMatrix)()
						this.data.onArInit()
					}
				} else {
					// ===== 原始状态处理逻辑 - 已注释 =====
					/*
					if (this.data.cameraTrackingState === 1 && currCameraTrackingState === 2) {
						console.log('[handleOnTick] AR追踪丢失 - 状态变化: 1 → 2')
						this.setData({
							cameraTrackingState: 2
						})
						this.data.onArLost()
						console.log('[handleOnTick] AR追踪丢失，调用onArLost后退出本次tick')
						// return
					}

					if (this.data.cameraTrackingState === 2 && currCameraTrackingState === 1) {
						console.log('[handleOnTick] AR追踪恢复 - 状态变化: 2 → 1')
						this.setData({
							cameraTrackingState: 1
						})
						this.data.onArTracked()
					}
					*/

					// ===== START_TICK_STATE_FIX: 改进的状态处理逻辑 =====
					if (this.data.cameraTrackingState === 1 && currCameraTrackingState === 2) {
						console.log('[handleOnTick] AR追踪丢失 - 状态变化: 1 → 2')
						this.setData({
							cameraTrackingState: 2
						})
						this.data.onArLost()
						console.log('[handleOnTick] AR追踪丢失，但继续执行tick逻辑')
						// 不要return，继续执行下面的逻辑
					}

					if (this.data.cameraTrackingState === 2 && currCameraTrackingState === 1) {
						console.log('[handleOnTick] AR追踪恢复 - 状态变化: 2 → 1')
						this.setData({
							cameraTrackingState: 1
						})
						// 添加延迟，避免频繁触发恢复
						if (!this.arRecoveryTimer) {
							this.arRecoveryTimer = setTimeout(() => {
								this.data.onArTracked()
								this.arRecoveryTimer = null
								console.log('[handleOnTick] AR恢复回调已延迟执行')
							}, 1000)  // 延迟1秒触发恢复
							console.log('[handleOnTick] AR恢复已设置延迟回调')
						} else {
							console.log('[handleOnTick] AR恢复延迟回调已存在，跳过')
						}
					}
					// ===== END_TICK_STATE_FIX =====
					// console.log("prevPos: x: "+prevPos.x+", y: "+prevPos.y+", z: "+prevPos.z)
					// console.log("worldPos: x: "+worldPos.x+", y: "+worldPos.y+", z: "+worldPos.z)
					prevPos.setValue(worldPos.x, worldPos.y, worldPos.z)
					let arSystemCorrectedPose = {}
					if (this.arSystemTransformMatrix) {
						arSystemCorrectedPose = (getTransformedPose || localGetTransformedPose)(
							Object.values(this.arSystemTransformMatrix),
							originalPose
						)
						this.updateCameraPose(arSystemCorrectedPose)
					}
					// this.triggerEvent('cameraPoseTick', {
					//   cameraPos: worldPos,
					//   cameraQuat: worldQuat,
					//   arRawData: this.arRawData
					// })
					this.data.onCameraPoseTick({
						cameraPos: arSystemCorrectedPose?.position,
						cameraQuat: arSystemCorrectedPose?.quaternion,
						arRawData: this.arRawData
					})
				}
				if (this.data.initialVpsTracked) {
					if (this.renderController) {
						this.renderController.update()
					}
				}

				// 添加正常tick完成的日志
			} catch (err) {
				console.error('[handleOnTick] 发生错误:', err)
				console.error('[handleOnTick] 错误堆栈:', err.stack)
				console.log('[handleOnTick] 错误发生时的状态:', {
					cameraTrackingState: this.data.cameraTrackingState,
					isArCoordSystemInit: this.data.isArCoordSystemInit,
					isTracking: this.data.isTracking,
					initialVpsTracked: this.data.initialVpsTracked
				})
			}
		},
		async loadAssetList(assetList) {
			if (!assetList?.length || assetList.length === 0) {
				console.warn('No assets to load.')
				return
			}

      this.setData({
        assetListLoadCount: this.data.assetListLoadCount + 1
      })

			// 将 list 转为 Map，便于索引或后续管理
			const assetMap = new Map(assetList.map((asset) => [asset.id, asset]))

			try {
				const assetPromises = Array.from(assetMap.entries()).map(([id, asset]) =>
					this.loadGLTFAsset(asset.id, asset.src).then((result) => {
						if (!result) throw new Error(`Asset ${id} failed to load`)
						return result
					})
				)
				await Promise.all(assetPromises)
				this.assetMap = assetMap
				this.setData({ assetListLoaded: true })
				this.data.onAssetListLoaded(true)
				console.log('assetList loaded')
			} catch (err) {
				console.error('Failed to load one or more assets:', err)
				throw err
			}
		},
		unloadAssetList() {
			if (this.assetMap) {
				try {
					Array.from(this.assetMap.values()).forEach((asset) => {
						if (this.scene && this.scene.assets) {
							this.scene.assets.releaseAsset(asset.type, asset.id)
						}
					})
					this.assetMap.clear() // 清空Map
					this.assetMap = null
					console.log('资产列表已卸载')
				} catch (error) {
					console.error('卸载资产列表时出错:', error)
					this.assetMap = null
				}
			}
			// 重置资产加载状态
			this.setData({ assetListLoaded: false })
		},
		updateCameraPose(correctedPose) {
			try {
				if (!correctedPose || !correctedPose.position || !correctedPose.quaternion) {
					throw new Error('缺少位置或旋转数据')
				}
				this.cameraTrs.position.set(
					xr.Vector3.createFromNumber(
						correctedPose.position.x,
						correctedPose.position.y,
						correctedPose.position.z
					)
				)
				this.cameraTrs.quaternion.set(correctedPose.quaternion)
			} catch (err) {
				console.log('[updateCameraPose]: ' + err)
			}
		},
		// ===== 原始pauseTracking方法 - 已注释 =====
		/*
		pauseTracking() {
			if (this.data.isTracking) {
				// 清理tick事件监听
				if (this.scene && this.scene.event) {
					this.scene.event.clear('tick')
				}
				// 清空回调引用，防止内存泄漏
				console.warn('===============================================')
				console.warn('pauseTracking')
				console.warn('===============================================')
				this.onTickCallback = null
				this.setData({
					isTracking: false
				})
			}
		},
		*/
		// ===== START_PAUSE_TRACKING_FIX: 增强的pauseTracking方法 =====
		pauseTracking() {
			console.log('[pauseTracking] 开始停止追踪')

			// 1. 先停止追踪状态，防止新的tick回调
			this.setData({
				isTracking: false
			})

			// 2. 如果有具体的回调引用，先移除
			if (this.onTickCallback && this.scene && this.scene.event) {
				try {
					console.log('[pauseTracking] 移除具体的tick回调')
					this.scene.event.remove('tick', this.onTickCallback)
				} catch (error) {
					console.warn('[pauseTracking] 移除具体回调失败:', error)
				}
			}

			// 3. 清除所有tick事件监听器（兜底方案）
			if (this.scene && this.scene.event) {
				try {
					console.log('[pauseTracking] 清除所有tick事件监听器')
					this.scene.event.clear('tick')
				} catch (error) {
					console.warn('[pauseTracking] 清除所有tick监听器失败:', error)
				}
			}

			// 4. 清空回调引用，防止内存泄漏
			this.onTickCallback = null

			console.log('[pauseTracking] 追踪停止完成')
		},
		// ===== END_PAUSE_TRACKING_FIX =====
		resumeTracking() {
			console.warn('===============================================')
			console.warn('resumeTracking started')
			console.warn('===============================================')
			if (!this.data.isTracking) {
				if (!this.cameraTrs) {
					console.error('Cannot find cameraTrs')
					return
				}
				if (!this.arRawData) {
					console.error('Cannot find arRawData')
					return
				}
				console.warn('===============================================')
				console.warn('resumeTracking doing')
				console.warn('===============================================')

				this.onTickCallback = (deltaTime) => this.handleOnTick(deltaTime)
				this.scene.event.add('tick', this.onTickCallback)
				this.setData({
					isTracking: true
				})
				console.warn('resumeTracking done')
				console.warn('===============================================')
			}
		},
		spawnCameraPoseMesh() {
			const cameraEuler = this.cameraTrs.quaternion.toEulerAngles()
			const cameraPos = this.cameraTrs.position

			const meshNode = this.scene.createElement(xr.XRNode, {
				id: 'meshNode-' + Math.random(),
				rotation: `${(cameraEuler.x * 180) / Math.PI} ${(cameraEuler.y * 180) / Math.PI} ${(cameraEuler.z * 180) / Math.PI}`,
				position: `${cameraPos.x} ${cameraPos.y} ${cameraPos.z}`
			})

			const meshX = this.scene.createElement(xr.XRMesh, {
				position: `0.05 0 0`,
				scale: `0.1 0.02 0.02`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.7 0.3 0.3 1'
			})
			const meshY = this.scene.createElement(xr.XRMesh, {
				position: `0 0.05 0`,
				scale: `0.02 0.1 0.02`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.3 0.7 0.3 1'
			})
			const meshZ = this.scene.createElement(xr.XRMesh, {
				position: `0 0 0.05`,
				scale: `0.02 0.02 0.1`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.3 0.3 0.7 1'
			})
			const root = this.scene.getElementById('root')
			if (!root) {
				console.error('Root element not found')
				return
			}
			meshNode.addChild(meshX)
			meshNode.addChild(meshY)
			meshNode.addChild(meshZ)
			root.addChild(meshNode)
			if (this.renderController) {
				this.renderController.register(meshNode)
			}
			console.log(
				'生成queryPose位姿: position x: ' +
					cameraPos.x +
					', y: ' +
					cameraPos.y +
					', z: ' +
					cameraPos.z +
					', rotation x: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.x) +
					', y: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.y) +
					', z: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.z)
			)
		},
		async spawnAnchorItem(anchor) {
			console.log(`[spawnAnchorItem] 开始处理锚点: ${anchor.id}`)

			if (!anchor || !anchor.position || !anchor.rotation || !anchor.scale) {
				console.error('[spawnAnchorItem] Invalid anchor data:', anchor)
				return
			}

			console.log(`[spawnAnchorItem] 获取root元素`)
			const root = this.scene.getElementById('root')
			if (!root) {
				console.error('[spawnAnchorItem] Root element not found')
				return
			}
			console.log(`[spawnAnchorItem] root元素获取成功`)

			try {
				console.log(`[spawnAnchorItem] 检查是否存在现有节点: ${anchor.name}`)
				let meshNode = root.getChildByName(anchor.name)

				if (meshNode) {
					console.log(`[spawnAnchorItem] 更新现有节点: ${anchor.name}`)
					const meshNodeTrs = meshNode.getComponent(xr.Transform)
					meshNodeTrs.position.setValue(anchor.position.x, anchor.position.y, anchor.position.z)
					meshNodeTrs.rotation.setValue(
						(anchor.rotation.x * Math.PI) / 180,
						(anchor.rotation.y * Math.PI) / 180,
						(anchor.rotation.z * Math.PI) / 180
					)

					console.log(`[spawnAnchorItem] 获取GLTF子节点: gltf-${anchor.id}`)
					const gltfTrs = meshNode.getChildByName(`gltf-${anchor.id}`).getComponent(xr.Transform)
					gltfTrs.scale.setValue(anchor.scale.x, anchor.scale.y, anchor.scale.z)
					console.log(`[spawnAnchorItem] 现有节点更新完成: ${anchor.name}`)
				} else {
					console.log(`[spawnAnchorItem] 创建新节点: ${anchor.name}`)
					meshNode = this.scene.createElement(xr.XRNode, {
						id: anchor.id,
						name: anchor.name,
						position: `${anchor.position.x} ${anchor.position.y} ${anchor.position.z}`,
						rotation: `${anchor.rotation.x} ${anchor.rotation.y} ${anchor.rotation.z}`
					})
					console.log(`[spawnAnchorItem] 新节点创建成功: ${anchor.name}`)

					// Load or fetch GLTF asset
					console.log(`[spawnAnchorItem] 开始加载GLTF资产: ${anchor.assetId}`)
					let gltfAsset = await this.loadGLTFAsset(anchor.assetId, anchor.src)
					if (!gltfAsset) {
						console.error(`[spawnAnchorItem] Failed to load GLTF asset for: ${anchor.assetId}`)
						return
					}
					console.log(`[spawnAnchorItem] GLTF资产加载成功: ${anchor.assetId}`)

					// Add GLTF instances for each world pose
					console.log(`[spawnAnchorItem] 创建GLTF元素: gltf-${anchor.id}`)
					const gltf = this.scene.createElement(xr.XRGLTF, {
						name: `gltf-${anchor.id}`,
						position: `0 0 0`,
						rotation: `0 0 180`,
						scale: `${anchor.scale.x} ${anchor.scale.y} ${anchor.scale.z}`,
						'anim-autoplay': ''
					})
					console.log(`[spawnAnchorItem] GLTF元素创建成功: gltf-${anchor.id}`)

					console.log(`[spawnAnchorItem] 设置GLTF数据: ${anchor.assetId}`)
					gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value })
					console.log(`[spawnAnchorItem] GLTF数据设置成功: ${anchor.assetId}`)

					console.log(`[spawnAnchorItem] 添加到场景树`)
					meshNode.addChild(gltf)
					root.addChild(meshNode)
					console.log(`[spawnAnchorItem] 场景树添加成功`)

					console.log(`GLTF asset loaded and added to scene: ${anchor.assetId}`)
				}

				console.log(`[spawnAnchorItem] 注册到渲染控制器`)
				if (this.renderController) {
					this.renderController.register(meshNode)
				}
				console.log(`[spawnAnchorItem] 锚点处理完成: ${anchor.id}`)
			} catch (error) {
				console.error(`[spawnAnchorItem] Error loading GLTF item ${anchor.id}:`, error)
				// 添加堆栈信息
				console.error(`[spawnAnchorItem] Stack trace:`, error.stack)
			}
		},
		async loadGLTFAsset(assetId, url) {
			const scene = this.scene

			if (!scene) {
				console.log('scene is undefined')
				return
			}
			// Check if asset already exists
			let gltfAsset = scene.assets.getAssetWithState('gltf', assetId)
			console.log(
				assetId + ' gltfAsset is null: ' + !gltfAsset.value + ', state: ' + gltfAsset.state
			)
			if (!gltfAsset || !gltfAsset.value) {
				try {
					console.log('loadGLTFAsset id: ' + assetId)
					// Load asset if not found
					gltfAsset = await scene.assets.loadAsset({
						type: 'gltf',
						assetId,
						src: url
					})
				} catch (err) {
					console.error('Asset loading error:', err)
					return null
				}
			}

			return gltfAsset
		},
		async takeScreenshot(saveType) {
			// 判断当前客户端是否支持分享系统
			const supported = this.scene.share.supported

			if (!supported) {
				console.log('客户端不支持分享系统')
			}

			// 截取配置，可选`type`为`jpg`或`png`，在为`jpg`时，可配置`0~1`的`quality`
			// 以下是默认值
			const options = {
				type: 'jpg',
				quality: 0.8
			}

			// const buffer = await this.scene.share.captureToArrayBufferAsync(options);
			// return buffer
			const base64 = await this.scene.share.captureToDataURLAsync(options)
			return base64
		},

		/**
		 * 完整的清理方法，确保所有资源都被正确释放
		 */
		cleanup() {
<<<<<<< HEAD
			console.log('[cleanup] 开始彻底清理AR组件资源')

			// ===== START_ENHANCED_CLEANUP_FIX: 增强的资源清理 =====
			// 1. 强制停止追踪（多重保障）
			console.log('[cleanup] 执行强制停止追踪')
=======
      console.log('开始cleanup')
			// 1. 停止追踪
>>>>>>> feat/ar-1.1
			this.pauseTracking()

			// 2. 额外的tick事件清理保障（防止pauseTracking失效）
			if (this.scene && this.scene.event) {
				try {
					console.log('[cleanup] 再次清理tick事件（安全保障）')
					this.scene.event.clear('tick')
					// 清理可能的其他事件
					this.scene.event.clear('asset-progress')
					this.scene.event.clear('asset-loaded')
					this.scene.event.clear('asset-error')
				} catch (error) {
					console.warn('[cleanup] 事件清理过程中出错:', error)
				}
			}

			// 3. 重置追踪状态标志（防止幽灵状态）
			this.setData({
				isTracking: false,
				isArCoordSystemInit: false,
				arReady: false,
				cameraTrackingState: -1
			})
			// ===== END_ENHANCED_CLEANUP_FIX =====

			// 4. 清理资产列表
			this.unloadAssetList()

			// 5. 清理渲染控制器
			if (this.renderController) {
				this.renderController.clear()
				this.renderController = null
			}

			// 6. 清理相机状态管理器
			if (this.cameraStateManager) {
				this.cameraStateManager = null
			}

			// ===== START_CLEANUP_TIMER_FIX: 清理定时器 =====
			// 7. 清理AR恢复定时器，防止内存泄漏
			if (this.arRecoveryTimer) {
				clearTimeout(this.arRecoveryTimer)
				this.arRecoveryTimer = null
				console.log('[cleanup] AR恢复定时器已清理')
			}
			// ===== END_CLEANUP_TIMER_FIX =====

			// 8. 清理XR相关引用
			this.cameraTrs = null
			this.camera = null
			this.arRawData = null
			this.onTickCallback = null  // 确保回调引用被清空
			this.scene = null
			this.mat = null

			// 9. 清理变换矩阵
			this.arSystemTransformMatrix = null

			// 10. 重置全局变量
			prevPos = null

			// 11. 强制垃圾回收（如果支持）
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}

<<<<<<< HEAD
			console.log('[cleanup] AR组件资源清理完成')
=======
      console.log('结束cleanup')
>>>>>>> feat/ar-1.1
		}
	}
})
